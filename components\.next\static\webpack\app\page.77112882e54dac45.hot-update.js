"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/ErrorBoundary */ \"(app-pages-browser)/./app/components/ErrorBoundary.tsx\");\n/* harmony import */ var _components_CustomChatMessage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/CustomChatMessage */ \"(app-pages-browser)/./app/components/CustomChatMessage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ChatPage() {\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"welcome-message\",\n            role: \"assistant\",\n            content: \"您好！我是您的AI助手，可以帮您查询和分析文档内容。请问有什么可以帮助您的吗？\"\n        }\n    ]);\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 自动滚动到底部\n    const scrollToBottom = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages,\n        scrollToBottom\n    ]);\n    // 复刻 Vercel AI SDK 的流式响应解析逻辑\n    const parseStreamChunk = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((chunk)=>{\n        const lines = chunk.split(\"\\n\").filter((line)=>line.trim());\n        let content = \"\";\n        for (const line of lines){\n            // 解析 Vercel AI SDK 格式: 0:\"text content\"\n            if (line.startsWith(\"0:\")) {\n                try {\n                    // 提取引号内的内容\n                    const match = line.match(/^0:\"(.*)\"/);\n                    if (match) {\n                        // 处理 Unicode 转义序列\n                        const rawContent = match[1];\n                        const decodedContent = rawContent.replace(/\\\\u([0-9a-fA-F]{4})/g, (_, code)=>{\n                            return String.fromCharCode(parseInt(code, 16));\n                        });\n                        content += decodedContent;\n                    }\n                } catch (error) {\n                    console.warn(\"Failed to parse stream chunk:\", line, error);\n                }\n            } else if (line.match(/^\\d+:/)) {\n                // 可以在这里处理其他类型的流数据\n                console.log(\"Other stream data:\", line);\n            }\n        }\n        return content;\n    }, []);\n    const sendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (content)=>{\n        if (!content.trim() || isLoading) return;\n        const userMessage = {\n            id: Date.now().toString(),\n            role: \"user\",\n            content: content.trim()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInput(\"\");\n        setIsLoading(true);\n        // 创建助手消息占位符\n        const assistantMessage = {\n            id: (Date.now() + 1).toString(),\n            role: \"assistant\",\n            content: \"\"\n        };\n        setMessages((prev)=>[\n                ...prev,\n                assistantMessage\n            ]);\n        try {\n            var _response_body;\n            const response = await fetch(\"/api/chat\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    id: \"chat-\".concat(Date.now()),\n                    messages: [\n                        ...messages,\n                        userMessage\n                    ].map((msg)=>({\n                            role: msg.role,\n                            content: msg.content\n                        }))\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const reader = (_response_body = response.body) === null || _response_body === void 0 ? void 0 : _response_body.getReader();\n            if (!reader) throw new Error(\"No response body\");\n            let fullContent = \"\";\n            const decoder = new TextDecoder();\n            while(true){\n                const { done, value } = await reader.read();\n                if (done) break;\n                const chunk = decoder.decode(value, {\n                    stream: true\n                });\n                const parsedContent = parseStreamChunk(chunk);\n                if (parsedContent) {\n                    fullContent += parsedContent;\n                    // 实时更新助手消息内容\n                    setMessages((prev)=>prev.map((msg)=>msg.id === assistantMessage.id ? {\n                                ...msg,\n                                content: fullContent\n                            } : msg));\n                }\n            }\n        } catch (error) {\n            console.error(\"Error sending message:\", error);\n            // 更新助手消息为错误信息\n            setMessages((prev)=>prev.map((msg)=>msg.id === assistantMessage.id ? {\n                        ...msg,\n                        content: \"抱歉，发生了错误。请稍后再试。\"\n                    } : msg));\n        } finally{\n            setIsLoading(false);\n        }\n    }, [\n        messages,\n        isLoading,\n        parseStreamChunk\n    ]);\n    const handleSubmit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        e.preventDefault();\n        sendMessage(input);\n    }, [\n        input,\n        sendMessage\n    ]);\n    const handleKeyDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            sendMessage(input);\n        }\n    }, [\n        input,\n        sendMessage\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_2__.ErrorBoundary, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full flex flex-col bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-auto p-4 space-y-4\",\n                    children: [\n                        messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CustomChatMessage__WEBPACK_IMPORTED_MODULE_3__.CustomChatMessage, {\n                                message: message,\n                                isLoading: isLoading && message.role === \"assistant\" && message === messages[messages.length - 1]\n                            }, message.id, false, {\n                                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 13\n                            }, this)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: messagesEndRef\n                        }, void 0, false, {\n                            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t bg-background p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                value: input,\n                                onChange: (e)=>setInput(e.target.value),\n                                onKeyDown: handleKeyDown,\n                                placeholder: \"输入您的问题...\",\n                                className: \"flex-1 min-h-[44px] max-h-32 px-3 py-2 border border-input bg-background text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 resize-none rounded-md\",\n                                disabled: isLoading,\n                                rows: 1\n                            }, void 0, false, {\n                                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: isLoading || !input.trim(),\n                                className: \"px-4 py-2 bg-primary text-primary-foreground hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 rounded-md\",\n                                children: isLoading ? \"发送中...\" : \"发送\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n            lineNumber: 177,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n        lineNumber: 176,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatPage, \"tTsXm/0TJ9/i656oBZQYllfPnzY=\");\n_c = ChatPage;\nvar _c;\n$RefreshReg$(_c, \"ChatPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});