"use client";

import { ChatSection, ChatMessages, ChatInput } from "@llamaindex/chat-ui";
import { useChat } from "ai/react";
import { ErrorBoundary } from "./components/ErrorBoundary";
import { useState, useMemo } from "react";

export default function ChatPage() {
  // 使用 useState 确保 chatId 在组件生命周期内稳定
  const [chatId] = useState(() => `chat-${Date.now()}`);

  // 使用 useMemo 创建稳定的初始消息数组
  const initialMessages = useMemo(
    () => [
      {
        id: "1",
        role: "assistant" as const,
        content:
          "您好！我是您的AI助手，可以帮您查询和分析文档内容。请问有什么可以帮助您的吗？",
      },
    ],
    []
  );

  // 使用 useMemo 创建稳定的配置对象
  const chatConfig = useMemo(
    () => ({
      api: "/api/chat",
      body: { id: chatId },
      initialMessages,
    }),
    [chatId, initialMessages]
  );

  const handler = useChat(chatConfig);

  return (
    <ErrorBoundary>
      <div className="h-full flex flex-col">
        <ChatSection handler={handler as any} className="flex-1 flex flex-col">
          <ChatMessages />
          <ChatInput />
        </ChatSection>
      </div>
    </ErrorBoundary>
  );
}
