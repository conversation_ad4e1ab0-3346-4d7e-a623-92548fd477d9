"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/components/CitationTooltip.tsx":
/*!********************************************!*\
  !*** ./app/components/CitationTooltip.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CitationTooltip: function() { return /* binding */ CitationTooltip; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _LoadingSpinner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./LoadingSpinner */ \"(app-pages-browser)/./app/components/LoadingSpinner.tsx\");\n/* __next_internal_client_entry_do_not_use__ CitationTooltip auto */ \nvar _s = $RefreshSig$();\n\n\nfunction CitationTooltip(param) {\n    let { citationId, children } = param;\n    _s();\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [citationData, setCitationData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [position, setPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const tooltipRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const triggerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 使用 useCallback 创建稳定的加载函数\n    const loadCitationData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (citationData || isLoading) return;\n        setIsLoading(true);\n        try {\n            var _data_metadata;\n            const response = await fetch(\"/api/citation/\".concat(citationId));\n            if (!response.ok) throw new Error(\"Failed to load citation\");\n            const data = await response.json();\n            setCitationData({\n                id: data.id || citationId,\n                title: ((_data_metadata = data.metadata) === null || _data_metadata === void 0 ? void 0 : _data_metadata.file_name) || \"Document \".concat(citationId.substring(0, 8), \"...\"),\n                content: data.text || data.content || \"内容不可用\",\n                metadata: data.metadata\n            });\n        } catch (error) {\n            console.error(\"Error loading citation:\", error);\n            setCitationData({\n                id: citationId,\n                title: \"Error\",\n                content: \"Failed to load citation content\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    }, [\n        citationId,\n        citationData,\n        isLoading\n    ]);\n    const handleMouseEnter = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        var _triggerRef_current;\n        const rect = (_triggerRef_current = triggerRef.current) === null || _triggerRef_current === void 0 ? void 0 : _triggerRef_current.getBoundingClientRect();\n        if (rect) {\n            setPosition({\n                x: rect.left + rect.width / 2,\n                y: rect.top - 10\n            });\n        }\n        setIsVisible(true);\n        loadCitationData();\n    }, [\n        loadCitationData\n    ]);\n    const handleMouseLeave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setIsVisible(false);\n    }, []); // 无依赖项，函数稳定\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isVisible && tooltipRef.current) {\n            const tooltip = tooltipRef.current;\n            const rect = tooltip.getBoundingClientRect();\n            const viewportWidth = window.innerWidth;\n            const viewportHeight = window.innerHeight;\n            let adjustedX = position.x - rect.width / 2;\n            let adjustedY = position.y - rect.height;\n            // 确保tooltip不超出视口\n            if (adjustedX < 10) adjustedX = 10;\n            if (adjustedX + rect.width > viewportWidth - 10) {\n                adjustedX = viewportWidth - rect.width - 10;\n            }\n            if (adjustedY < 10) adjustedY = position.y + 30;\n            tooltip.style.left = \"\".concat(adjustedX, \"px\");\n            tooltip.style.top = \"\".concat(adjustedY, \"px\");\n        }\n    }, [\n        isVisible,\n        position.x,\n        position.y\n    ]); // 移除 citationData 依赖，避免无限循环\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                ref: triggerRef,\n                onMouseEnter: handleMouseEnter,\n                onMouseLeave: handleMouseLeave,\n                className: \"citation-number\",\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CitationTooltip.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this),\n            isVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: tooltipRef,\n                className: \"citation-tooltip fixed z-50\",\n                style: {\n                    left: position.x,\n                    top: position.y\n                },\n                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LoadingSpinner__WEBPACK_IMPORTED_MODULE_2__.LoadingSpinner, {\n                            size: \"sm\",\n                            className: \"text-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CitationTooltip.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Loading...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CitationTooltip.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CitationTooltip.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 13\n                }, this) : citationData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"font-semibold text-gray-900 mb-2\",\n                            children: citationData.title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CitationTooltip.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-700 text-sm leading-relaxed\",\n                            children: citationData.content.length > 300 ? \"\".concat(citationData.content.substring(0, 300), \"...\") : citationData.content\n                        }, void 0, false, {\n                            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CitationTooltip.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CitationTooltip.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 13\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: \"Error loading citation\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CitationTooltip.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CitationTooltip.tsx\",\n                lineNumber: 115,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(CitationTooltip, \"WX/qzkDisT45cF0X9qVPvxJQi28=\");\n_c = CitationTooltip;\nvar _c;\n$RefreshReg$(_c, \"CitationTooltip\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/CitationTooltip.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/components/CustomChatMessage.tsx":
/*!**********************************************!*\
  !*** ./app/components/CustomChatMessage.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomChatMessage: function() { return /* binding */ CustomChatMessage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _CitationTooltip__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CitationTooltip */ \"(app-pages-browser)/./app/components/CitationTooltip.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ CustomChatMessage auto */ \nvar _s = $RefreshSig$();\n\n\nfunction CustomChatMessage(param) {\n    let { message, isLoading } = param;\n    _s();\n    // 使用 useMemo 优化处理引用标记的函数，基于消息内容进行缓存\n    const processContent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return (content)=>{\n            // 查找所有引用标记\n            const citationRegex = /\\[citation:(.*?)\\]/g;\n            const citations = [];\n            let match;\n            while((match = citationRegex.exec(content)) !== null){\n                citations.push(match[1]);\n            }\n            // 替换引用标记为带tooltip的序号\n            let processedContent = content;\n            citations.forEach((citationId, index)=>{\n                const citationNumber = index + 1;\n                const citationPattern = new RegExp(\"\\\\[citation:\".concat(citationId.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\"), \"\\\\]\"), \"g\");\n                processedContent = processedContent.replace(citationPattern, '<citation-placeholder data-citation-id=\"'.concat(citationId, '\" data-citation-number=\"').concat(citationNumber, '\"></citation-placeholder>'));\n            });\n            return processedContent;\n        };\n    }, [\n        message.content\n    ]); // 依赖消息内容，只有内容变化时才重新计算\n    // 使用 useMemo 优化渲染处理后的内容\n    const renderContent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return (content)=>{\n            const processedContent = processContent(content);\n            // 分割内容并处理引用占位符\n            const parts = processedContent.split(/(<citation-placeholder[^>]*><\\/citation-placeholder>)/g);\n            return parts.map((part, index)=>{\n                const citationMatch = part.match(/<citation-placeholder data-citation-id=\"([^\"]*)\" data-citation-number=\"([^\"]*)\"><\\/citation-placeholder>/);\n                if (citationMatch) {\n                    const [, citationId, citationNumber] = citationMatch;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CitationTooltip__WEBPACK_IMPORTED_MODULE_1__.CitationTooltip, {\n                        citationId: citationId,\n                        children: [\n                            \"[\",\n                            citationNumber,\n                            \"]\"\n                        ]\n                    }, \"citation-\".concat(citationId, \"-\").concat(index), true, {\n                        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 13\n                    }, this);\n                }\n                return part;\n            });\n        };\n    }, [\n        processContent,\n        message.content\n    ]); // 依赖 processContent 和消息内容\n    // 如果是助手消息且包含引用，使用自定义渲染\n    if (message.role === \"assistant\" && message.content.includes(\"[citation:\")) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex \".concat(message.role === \"user\" ? \"justify-end\" : \"justify-start\", \" mb-4 chat-message\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-[80%] rounded-lg px-4 py-3 \".concat(message.role === \"user\" ? \"bg-primary text-primary-foreground\" : \"bg-muted text-muted-foreground border border-border\", \" shadow-sm\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"prose prose-sm max-w-none dark:prose-invert\",\n                        children: renderContent(message.content)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 11\n                    }, this),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mt-3 space-x-2 text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-3 h-3 border-2 border-current border-t-transparent rounded-full animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs\",\n                                children: \"正在思考...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n                lineNumber: 92,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n            lineNumber: 87,\n            columnNumber: 7\n        }, this);\n    }\n    // 对于其他消息，使用简单的消息显示\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex \".concat(message.role === \"user\" ? \"justify-end\" : \"justify-start\", \" mb-4 chat-message\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-[80%] rounded-lg px-4 py-3 \".concat(message.role === \"user\" ? \"bg-primary text-primary-foreground\" : \"bg-muted text-muted-foreground border border-border\", \" shadow-sm\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"prose prose-sm max-w-none dark:prose-invert\",\n                    children: message.content\n                }, void 0, false, {\n                    fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 9\n                }, this),\n                isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center mt-3 space-x-2 text-muted-foreground\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-3 h-3 border-2 border-current border-t-transparent rounded-full animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs\",\n                            children: \"正在思考...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n            lineNumber: 120,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n        lineNumber: 115,\n        columnNumber: 5\n    }, this);\n}\n_s(CustomChatMessage, \"42MNKg14tSt5NIOut1GKAGGzxdc=\");\n_c = CustomChatMessage;\nvar _c;\n$RefreshReg$(_c, \"CustomChatMessage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/CustomChatMessage.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/components/LoadingSpinner.tsx":
/*!*******************************************!*\
  !*** ./app/components/LoadingSpinner.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoadingSpinner: function() { return /* binding */ LoadingSpinner; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ LoadingSpinner auto */ \nfunction LoadingSpinner(param) {\n    let { size = \"md\", className = \"\" } = param;\n    const sizeClasses = {\n        sm: \"w-4 h-4\",\n        md: \"w-6 h-6\",\n        lg: \"w-8 h-8\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(sizeClasses[size], \" border-2 border-current border-t-transparent rounded-full animate-spin \").concat(className)\n    }, void 0, false, {\n        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\LoadingSpinner.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n_c = LoadingSpinner;\nvar _c;\n$RefreshReg$(_c, \"LoadingSpinner\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9jb21wb25lbnRzL0xvYWRpbmdTcGlubmVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFPTyxTQUFTQSxlQUFlLEtBQW9EO1FBQXBELEVBQUVDLE9BQU8sSUFBSSxFQUFFQyxZQUFZLEVBQUUsRUFBdUIsR0FBcEQ7SUFDN0IsTUFBTUMsY0FBYztRQUNsQkMsSUFBSTtRQUNKQyxJQUFJO1FBQ0pDLElBQUk7SUFDTjtJQUVBLHFCQUNFLDhEQUFDQztRQUFJTCxXQUFXLEdBQStGQSxPQUE1RkMsV0FBVyxDQUFDRixLQUFLLEVBQUMsNEVBQW9GLE9BQVZDOzs7Ozs7QUFFbkg7S0FWZ0JGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2FwcC9jb21wb25lbnRzL0xvYWRpbmdTcGlubmVyLnRzeD84NGRiIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbnRlcmZhY2UgTG9hZGluZ1NwaW5uZXJQcm9wcyB7XG4gIHNpemU/OiAnc20nIHwgJ21kJyB8ICdsZydcbiAgY2xhc3NOYW1lPzogc3RyaW5nXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBMb2FkaW5nU3Bpbm5lcih7IHNpemUgPSAnbWQnLCBjbGFzc05hbWUgPSAnJyB9OiBMb2FkaW5nU3Bpbm5lclByb3BzKSB7XG4gIGNvbnN0IHNpemVDbGFzc2VzID0ge1xuICAgIHNtOiAndy00IGgtNCcsXG4gICAgbWQ6ICd3LTYgaC02JywgXG4gICAgbGc6ICd3LTggaC04J1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17YCR7c2l6ZUNsYXNzZXNbc2l6ZV19IGJvcmRlci0yIGJvcmRlci1jdXJyZW50IGJvcmRlci10LXRyYW5zcGFyZW50IHJvdW5kZWQtZnVsbCBhbmltYXRlLXNwaW4gJHtjbGFzc05hbWV9YH0gLz5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkxvYWRpbmdTcGlubmVyIiwic2l6ZSIsImNsYXNzTmFtZSIsInNpemVDbGFzc2VzIiwic20iLCJtZCIsImxnIiwiZGl2Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/LoadingSpinner.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/ErrorBoundary */ \"(app-pages-browser)/./app/components/ErrorBoundary.tsx\");\n/* harmony import */ var _components_CustomChatMessage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/CustomChatMessage */ \"(app-pages-browser)/./app/components/CustomChatMessage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ChatPage() {\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"welcome-message\",\n            role: \"assistant\",\n            content: \"您好！我是您的AI助手，可以帮您查询和分析文档内容。请问有什么可以帮助您的吗？\"\n        }\n    ]);\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 自动滚动到底部\n    const scrollToBottom = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages,\n        scrollToBottom\n    ]);\n    // 复刻 Vercel AI SDK 的流式响应解析逻辑\n    const parseStreamChunk = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((chunk)=>{\n        const lines = chunk.split(\"\\n\").filter((line)=>line.trim());\n        let content = \"\";\n        for (const line of lines){\n            // 解析 Vercel AI SDK 格式: 0:\"text content\"\n            if (line.startsWith(\"0:\")) {\n                try {\n                    // 提取引号内的内容\n                    const match = line.match(/^0:\"(.*)\"/);\n                    if (match) {\n                        // 处理 Unicode 转义序列\n                        const rawContent = match[1];\n                        const decodedContent = rawContent.replace(/\\\\u([0-9a-fA-F]{4})/g, (match, code)=>{\n                            return String.fromCharCode(parseInt(code, 16));\n                        });\n                        content += decodedContent;\n                    }\n                } catch (error) {\n                    console.warn(\"Failed to parse stream chunk:\", line, error);\n                }\n            } else if (line.match(/^\\d+:/)) {\n                // 可以在这里处理其他类型的流数据\n                console.log(\"Other stream data:\", line);\n            }\n        }\n        return content;\n    }, []);\n    const sendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (content)=>{\n        if (!content.trim() || isLoading) return;\n        const userMessage = {\n            id: Date.now().toString(),\n            role: \"user\",\n            content: content.trim()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInput(\"\");\n        setIsLoading(true);\n        // 创建助手消息占位符\n        const assistantMessage = {\n            id: (Date.now() + 1).toString(),\n            role: \"assistant\",\n            content: \"\"\n        };\n        setMessages((prev)=>[\n                ...prev,\n                assistantMessage\n            ]);\n        try {\n            var _response_body;\n            const response = await fetch(\"/api/chat\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    id: \"chat-\".concat(Date.now()),\n                    messages: [\n                        ...messages,\n                        userMessage\n                    ].map((msg)=>({\n                            role: msg.role,\n                            content: msg.content\n                        }))\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const reader = (_response_body = response.body) === null || _response_body === void 0 ? void 0 : _response_body.getReader();\n            if (!reader) throw new Error(\"No response body\");\n            let fullContent = \"\";\n            const decoder = new TextDecoder();\n            while(true){\n                const { done, value } = await reader.read();\n                if (done) break;\n                const chunk = decoder.decode(value, {\n                    stream: true\n                });\n                const parsedContent = parseStreamChunk(chunk);\n                if (parsedContent) {\n                    fullContent += parsedContent;\n                    // 实时更新助手消息内容\n                    setMessages((prev)=>prev.map((msg)=>msg.id === assistantMessage.id ? {\n                                ...msg,\n                                content: fullContent\n                            } : msg));\n                }\n            }\n        } catch (error) {\n            console.error(\"Error sending message:\", error);\n            // 更新助手消息为错误信息\n            setMessages((prev)=>prev.map((msg)=>msg.id === assistantMessage.id ? {\n                        ...msg,\n                        content: \"抱歉，发生了错误。请稍后再试。\"\n                    } : msg));\n        } finally{\n            setIsLoading(false);\n        }\n    }, [\n        messages,\n        isLoading,\n        parseStreamChunk\n    ]);\n    const handleSubmit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        e.preventDefault();\n        sendMessage(input);\n    }, [\n        input,\n        sendMessage\n    ]);\n    const handleKeyDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            sendMessage(input);\n        }\n    }, [\n        input,\n        sendMessage\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_2__.ErrorBoundary, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full flex flex-col bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-auto p-4 space-y-4\",\n                    children: [\n                        messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CustomChatMessage__WEBPACK_IMPORTED_MODULE_3__.CustomChatMessage, {\n                                message: message,\n                                isLoading: isLoading && message.role === \"assistant\" && message === messages[messages.length - 1]\n                            }, message.id, false, {\n                                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 13\n                            }, this)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: messagesEndRef\n                        }, void 0, false, {\n                            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t bg-background p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                value: input,\n                                onChange: (e)=>setInput(e.target.value),\n                                onKeyDown: handleKeyDown,\n                                placeholder: \"输入您的问题...\",\n                                className: \"flex-1 min-h-[44px] max-h-32 px-3 py-2 border border-input bg-background text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 resize-none rounded-md\",\n                                disabled: isLoading,\n                                rows: 1\n                            }, void 0, false, {\n                                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: isLoading || !input.trim(),\n                                className: \"px-4 py-2 bg-primary text-primary-foreground hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 rounded-md\",\n                                children: isLoading ? \"发送中...\" : \"发送\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n            lineNumber: 177,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n        lineNumber: 176,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatPage, \"tTsXm/0TJ9/i656oBZQYllfPnzY=\");\n_c = ChatPage;\nvar _c;\n$RefreshReg$(_c, \"ChatPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});