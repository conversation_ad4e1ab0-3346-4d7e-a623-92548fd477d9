"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ChatInput_ChatMessages_ChatSection_llamaindex_chat_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChatInput,ChatMessages,ChatSection!=!@llamaindex/chat-ui */ \"(app-pages-browser)/./node_modules/@llamaindex/chat-ui/dist/chat/index.js\");\n/* harmony import */ var ai_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ai/react */ \"(app-pages-browser)/./node_modules/ai/react/dist/index.mjs\");\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./components/ErrorBoundary */ \"(app-pages-browser)/./app/components/ErrorBoundary.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// 在组件外部定义静态配置，确保引用稳定性\nconst STATIC_CHAT_CONFIG = {\n    api: \"/api/chat\",\n    body: {\n        id: \"stable-chat\"\n    },\n    initialMessages: [\n        {\n            id: \"welcome-message\",\n            role: \"assistant\",\n            content: \"您好！我是您的AI助手，可以帮您查询和分析文档内容。请问有什么可以帮助您的吗？\"\n        }\n    ]\n};\nfunction ChatPage() {\n    _s();\n    // 直接使用静态配置，避免任何可能导致重新创建的操作\n    const handler = (0,ai_react__WEBPACK_IMPORTED_MODULE_2__.useChat)(STATIC_CHAT_CONFIG);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_1__.ErrorBoundary, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-full flex flex-col\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatInput_ChatMessages_ChatSection_llamaindex_chat_ui__WEBPACK_IMPORTED_MODULE_3__.ChatSection, {\n                handler: handler,\n                className: \"flex-1 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatInput_ChatMessages_ChatSection_llamaindex_chat_ui__WEBPACK_IMPORTED_MODULE_3__.ChatMessages, {}, void 0, false, {\n                        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatInput_ChatMessages_ChatSection_llamaindex_chat_ui__WEBPACK_IMPORTED_MODULE_3__.ChatInput, {}, void 0, false, {\n                        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\page.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatPage, \"1ZiBm6gpQFpcty49fMCYY+Iqv+w=\", false, function() {\n    return [\n        ai_react__WEBPACK_IMPORTED_MODULE_2__.useChat\n    ];\n});\n_c = ChatPage;\nvar _c;\n$RefreshReg$(_c, \"ChatPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});