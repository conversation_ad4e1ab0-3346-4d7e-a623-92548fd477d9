"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/components/CustomChatMessage.tsx":
/*!**********************************************!*\
  !*** ./app/components/CustomChatMessage.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomChatMessage: function() { return /* binding */ CustomChatMessage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _CitationTooltip__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CitationTooltip */ \"(app-pages-browser)/./app/components/CitationTooltip.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ CustomChatMessage auto */ \nvar _s = $RefreshSig$();\n\n\nfunction CustomChatMessage(param) {\n    let { message, isLoading } = param;\n    _s();\n    // 使用 useMemo 优化处理引用标记的函数，基于消息内容进行缓存\n    const processContent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return (content)=>{\n            // 查找所有引用标记\n            const citationRegex = /\\[citation:(.*?)\\]/g;\n            const citations = [];\n            let match;\n            while((match = citationRegex.exec(content)) !== null){\n                citations.push(match[1]);\n            }\n            // 替换引用标记为带tooltip的序号\n            let processedContent = content;\n            citations.forEach((citationId, index)=>{\n                const citationNumber = index + 1;\n                const citationPattern = new RegExp(\"\\\\[citation:\".concat(citationId.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\"), \"\\\\]\"), \"g\");\n                processedContent = processedContent.replace(citationPattern, '<citation-placeholder data-citation-id=\"'.concat(citationId, '\" data-citation-number=\"').concat(citationNumber, '\"></citation-placeholder>'));\n            });\n            return processedContent;\n        };\n    }, [\n        message.content\n    ]); // 依赖消息内容，只有内容变化时才重新计算\n    // 使用 useMemo 优化渲染处理后的内容\n    const renderContent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return (content)=>{\n            const processedContent = processContent(content);\n            // 分割内容并处理引用占位符\n            const parts = processedContent.split(/(<citation-placeholder[^>]*><\\/citation-placeholder>)/g);\n            return parts.map((part, index)=>{\n                const citationMatch = part.match(/<citation-placeholder data-citation-id=\"([^\"]*)\" data-citation-number=\"([^\"]*)\"><\\/citation-placeholder>/);\n                if (citationMatch) {\n                    const [, citationId, citationNumber] = citationMatch;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CitationTooltip__WEBPACK_IMPORTED_MODULE_1__.CitationTooltip, {\n                        citationId: citationId,\n                        children: [\n                            \"[\",\n                            citationNumber,\n                            \"]\"\n                        ]\n                    }, \"citation-\".concat(citationId, \"-\").concat(index), true, {\n                        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 13\n                    }, this);\n                }\n                return part;\n            });\n        };\n    }, [\n        processContent,\n        message.content\n    ]); // 依赖 processContent 和消息内容\n    // 如果是助手消息且包含引用，使用自定义渲染\n    if (message.role === \"assistant\" && message.content.includes(\"[citation:\")) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex \".concat(message.role === \"user\" ? \"justify-end\" : \"justify-start\", \" mb-4 chat-message\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-[80%] rounded-lg px-4 py-3 \".concat(message.role === \"user\" ? \"bg-primary text-primary-foreground\" : \"bg-muted text-muted-foreground border border-border\", \" shadow-sm\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"prose prose-sm max-w-none dark:prose-invert\",\n                        children: renderContent(message.content)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 11\n                    }, this),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mt-3 space-x-2 text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-3 h-3 border-2 border-current border-t-transparent rounded-full animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs\",\n                                children: \"正在思考...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n                lineNumber: 92,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n            lineNumber: 87,\n            columnNumber: 7\n        }, this);\n    }\n    // 对于其他消息，使用默认的ChatMessage组件\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatMessage, {\n        message: message,\n        isLoading: isLoading\n    }, void 0, false, {\n        fileName: \"D:\\\\create-lllama\\\\pyllamaindex\\\\components\\\\app\\\\components\\\\CustomChatMessage.tsx\",\n        lineNumber: 114,\n        columnNumber: 10\n    }, this);\n}\n_s(CustomChatMessage, \"42MNKg14tSt5NIOut1GKAGGzxdc=\");\n_c = CustomChatMessage;\nvar _c;\n$RefreshReg$(_c, \"CustomChatMessage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9jb21wb25lbnRzL0N1c3RvbUNoYXRNZXNzYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRW9EO0FBQ3BCO0FBYXpCLFNBQVNFLGtCQUFrQixLQUdUO1FBSFMsRUFDaENDLE9BQU8sRUFDUEMsU0FBUyxFQUNjLEdBSFM7O0lBSWhDLG9DQUFvQztJQUNwQyxNQUFNQyxpQkFBaUJKLDhDQUFPQSxDQUFDO1FBQzdCLE9BQU8sQ0FBQ0s7WUFDTixXQUFXO1lBQ1gsTUFBTUMsZ0JBQWdCO1lBQ3RCLE1BQU1DLFlBQXNCLEVBQUU7WUFDOUIsSUFBSUM7WUFFSixNQUFPLENBQUNBLFFBQVFGLGNBQWNHLElBQUksQ0FBQ0osUUFBTyxNQUFPLEtBQU07Z0JBQ3JERSxVQUFVRyxJQUFJLENBQUNGLEtBQUssQ0FBQyxFQUFFO1lBQ3pCO1lBRUEscUJBQXFCO1lBQ3JCLElBQUlHLG1CQUFtQk47WUFDdkJFLFVBQVVLLE9BQU8sQ0FBQyxDQUFDQyxZQUFZQztnQkFDN0IsTUFBTUMsaUJBQWlCRCxRQUFRO2dCQUMvQixNQUFNRSxrQkFBa0IsSUFBSUMsT0FDMUIsZUFBaUUsT0FBbERKLFdBQVdLLE9BQU8sQ0FBQyx1QkFBdUIsU0FBUSxRQUNqRTtnQkFHRlAsbUJBQW1CQSxpQkFBaUJPLE9BQU8sQ0FDekNGLGlCQUNBLDJDQUFnRkQsT0FBckNGLFlBQVcsNEJBQXlDLE9BQWZFLGdCQUFlO1lBRW5HO1lBRUEsT0FBT0o7UUFDVDtJQUNGLEdBQUc7UUFBQ1QsUUFBUUcsT0FBTztLQUFDLEdBQUcsc0JBQXNCO0lBRTdDLHdCQUF3QjtJQUN4QixNQUFNYyxnQkFBZ0JuQiw4Q0FBT0EsQ0FBQztRQUM1QixPQUFPLENBQUNLO1lBQ04sTUFBTU0sbUJBQW1CUCxlQUFlQztZQUV4QyxlQUFlO1lBQ2YsTUFBTWUsUUFBUVQsaUJBQWlCVSxLQUFLLENBQ2xDO1lBR0YsT0FBT0QsTUFBTUUsR0FBRyxDQUFDLENBQUNDLE1BQU1UO2dCQUN0QixNQUFNVSxnQkFBZ0JELEtBQUtmLEtBQUssQ0FDOUI7Z0JBR0YsSUFBSWdCLGVBQWU7b0JBQ2pCLE1BQU0sR0FBR1gsWUFBWUUsZUFBZSxHQUFHUztvQkFDdkMscUJBQ0UsOERBQUN6Qiw2REFBZUE7d0JBRWRjLFlBQVlBOzs0QkFDYjs0QkFDR0U7NEJBQWU7O3VCQUhaLFlBQTBCRCxPQUFkRCxZQUFXLEtBQVMsT0FBTkM7Ozs7O2dCQU1yQztnQkFFQSxPQUFPUztZQUNUO1FBQ0Y7SUFDRixHQUFHO1FBQUNuQjtRQUFnQkYsUUFBUUcsT0FBTztLQUFDLEdBQUcsMEJBQTBCO0lBRWpFLHVCQUF1QjtJQUN2QixJQUFJSCxRQUFRdUIsSUFBSSxLQUFLLGVBQWV2QixRQUFRRyxPQUFPLENBQUNxQixRQUFRLENBQUMsZUFBZTtRQUMxRSxxQkFDRSw4REFBQ0M7WUFDQ0MsV0FBVyxRQUVWLE9BREMxQixRQUFRdUIsSUFBSSxLQUFLLFNBQVMsZ0JBQWdCLGlCQUMzQztzQkFFRCw0RUFBQ0U7Z0JBQ0NDLFdBQVcsb0NBSVYsT0FIQzFCLFFBQVF1QixJQUFJLEtBQUssU0FDYix1Q0FDQSx1REFDTDs7a0NBRUQsOERBQUNFO3dCQUFJQyxXQUFVO2tDQUNaVCxjQUFjakIsUUFBUUcsT0FBTzs7Ozs7O29CQUUvQkYsMkJBQ0MsOERBQUN3Qjt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOzs7Ozs7MENBQ2YsOERBQUNDO2dDQUFLRCxXQUFVOzBDQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQU10QztJQUVBLDRCQUE0QjtJQUM1QixxQkFBTyw4REFBQ0U7UUFBWTVCLFNBQVNBO1FBQVNDLFdBQVdBOzs7Ozs7QUFDbkQ7R0FsR2dCRjtLQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9hcHAvY29tcG9uZW50cy9DdXN0b21DaGF0TWVzc2FnZS50c3g/OWI4NSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcclxuXHJcbmltcG9ydCB7IENpdGF0aW9uVG9vbHRpcCB9IGZyb20gXCIuL0NpdGF0aW9uVG9vbHRpcFwiO1xyXG5pbXBvcnQgeyB1c2VNZW1vIH0gZnJvbSBcInJlYWN0XCI7XHJcblxyXG5pbnRlcmZhY2UgTWVzc2FnZSB7XHJcbiAgaWQ6IHN0cmluZztcclxuICByb2xlOiBcInVzZXJcIiB8IFwiYXNzaXN0YW50XCI7XHJcbiAgY29udGVudDogc3RyaW5nO1xyXG59XHJcblxyXG5pbnRlcmZhY2UgQ3VzdG9tQ2hhdE1lc3NhZ2VQcm9wcyB7XHJcbiAgbWVzc2FnZTogTWVzc2FnZTtcclxuICBpc0xvYWRpbmc/OiBib29sZWFuO1xyXG59XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gQ3VzdG9tQ2hhdE1lc3NhZ2Uoe1xyXG4gIG1lc3NhZ2UsXHJcbiAgaXNMb2FkaW5nLFxyXG59OiBDdXN0b21DaGF0TWVzc2FnZVByb3BzKSB7XHJcbiAgLy8g5L2/55SoIHVzZU1lbW8g5LyY5YyW5aSE55CG5byV55So5qCH6K6w55qE5Ye95pWw77yM5Z+65LqO5raI5oGv5YaF5a656L+b6KGM57yT5a2YXHJcbiAgY29uc3QgcHJvY2Vzc0NvbnRlbnQgPSB1c2VNZW1vKCgpID0+IHtcclxuICAgIHJldHVybiAoY29udGVudDogc3RyaW5nKSA9PiB7XHJcbiAgICAgIC8vIOafpeaJvuaJgOacieW8leeUqOagh+iusFxyXG4gICAgICBjb25zdCBjaXRhdGlvblJlZ2V4ID0gL1xcW2NpdGF0aW9uOiguKj8pXFxdL2c7XHJcbiAgICAgIGNvbnN0IGNpdGF0aW9uczogc3RyaW5nW10gPSBbXTtcclxuICAgICAgbGV0IG1hdGNoO1xyXG5cclxuICAgICAgd2hpbGUgKChtYXRjaCA9IGNpdGF0aW9uUmVnZXguZXhlYyhjb250ZW50KSkgIT09IG51bGwpIHtcclxuICAgICAgICBjaXRhdGlvbnMucHVzaChtYXRjaFsxXSk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIOabv+aNouW8leeUqOagh+iusOS4uuW4pnRvb2x0aXDnmoTluo/lj7dcclxuICAgICAgbGV0IHByb2Nlc3NlZENvbnRlbnQgPSBjb250ZW50O1xyXG4gICAgICBjaXRhdGlvbnMuZm9yRWFjaCgoY2l0YXRpb25JZCwgaW5kZXgpID0+IHtcclxuICAgICAgICBjb25zdCBjaXRhdGlvbk51bWJlciA9IGluZGV4ICsgMTtcclxuICAgICAgICBjb25zdCBjaXRhdGlvblBhdHRlcm4gPSBuZXcgUmVnRXhwKFxyXG4gICAgICAgICAgYFxcXFxbY2l0YXRpb246JHtjaXRhdGlvbklkLnJlcGxhY2UoL1suKis/XiR7fSgpfFtcXF1cXFxcXS9nLCBcIlxcXFwkJlwiKX1cXFxcXWAsXHJcbiAgICAgICAgICBcImdcIlxyXG4gICAgICAgICk7XHJcblxyXG4gICAgICAgIHByb2Nlc3NlZENvbnRlbnQgPSBwcm9jZXNzZWRDb250ZW50LnJlcGxhY2UoXHJcbiAgICAgICAgICBjaXRhdGlvblBhdHRlcm4sXHJcbiAgICAgICAgICBgPGNpdGF0aW9uLXBsYWNlaG9sZGVyIGRhdGEtY2l0YXRpb24taWQ9XCIke2NpdGF0aW9uSWR9XCIgZGF0YS1jaXRhdGlvbi1udW1iZXI9XCIke2NpdGF0aW9uTnVtYmVyfVwiPjwvY2l0YXRpb24tcGxhY2Vob2xkZXI+YFxyXG4gICAgICAgICk7XHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgcmV0dXJuIHByb2Nlc3NlZENvbnRlbnQ7XHJcbiAgICB9O1xyXG4gIH0sIFttZXNzYWdlLmNvbnRlbnRdKTsgLy8g5L6d6LWW5raI5oGv5YaF5a6577yM5Y+q5pyJ5YaF5a655Y+Y5YyW5pe25omN6YeN5paw6K6h566XXHJcblxyXG4gIC8vIOS9v+eUqCB1c2VNZW1vIOS8mOWMlua4suafk+WkhOeQhuWQjueahOWGheWuuVxyXG4gIGNvbnN0IHJlbmRlckNvbnRlbnQgPSB1c2VNZW1vKCgpID0+IHtcclxuICAgIHJldHVybiAoY29udGVudDogc3RyaW5nKSA9PiB7XHJcbiAgICAgIGNvbnN0IHByb2Nlc3NlZENvbnRlbnQgPSBwcm9jZXNzQ29udGVudChjb250ZW50KTtcclxuXHJcbiAgICAgIC8vIOWIhuWJsuWGheWuueW5tuWkhOeQhuW8leeUqOWNoOS9jeesplxyXG4gICAgICBjb25zdCBwYXJ0cyA9IHByb2Nlc3NlZENvbnRlbnQuc3BsaXQoXHJcbiAgICAgICAgLyg8Y2l0YXRpb24tcGxhY2Vob2xkZXJbXj5dKj48XFwvY2l0YXRpb24tcGxhY2Vob2xkZXI+KS9nXHJcbiAgICAgICk7XHJcblxyXG4gICAgICByZXR1cm4gcGFydHMubWFwKChwYXJ0LCBpbmRleCkgPT4ge1xyXG4gICAgICAgIGNvbnN0IGNpdGF0aW9uTWF0Y2ggPSBwYXJ0Lm1hdGNoKFxyXG4gICAgICAgICAgLzxjaXRhdGlvbi1wbGFjZWhvbGRlciBkYXRhLWNpdGF0aW9uLWlkPVwiKFteXCJdKilcIiBkYXRhLWNpdGF0aW9uLW51bWJlcj1cIihbXlwiXSopXCI+PFxcL2NpdGF0aW9uLXBsYWNlaG9sZGVyPi9cclxuICAgICAgICApO1xyXG5cclxuICAgICAgICBpZiAoY2l0YXRpb25NYXRjaCkge1xyXG4gICAgICAgICAgY29uc3QgWywgY2l0YXRpb25JZCwgY2l0YXRpb25OdW1iZXJdID0gY2l0YXRpb25NYXRjaDtcclxuICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgIDxDaXRhdGlvblRvb2x0aXBcclxuICAgICAgICAgICAgICBrZXk9e2BjaXRhdGlvbi0ke2NpdGF0aW9uSWR9LSR7aW5kZXh9YH1cclxuICAgICAgICAgICAgICBjaXRhdGlvbklkPXtjaXRhdGlvbklkfVxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgW3tjaXRhdGlvbk51bWJlcn1dXHJcbiAgICAgICAgICAgIDwvQ2l0YXRpb25Ub29sdGlwPlxyXG4gICAgICAgICAgKTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIHJldHVybiBwYXJ0O1xyXG4gICAgICB9KTtcclxuICAgIH07XHJcbiAgfSwgW3Byb2Nlc3NDb250ZW50LCBtZXNzYWdlLmNvbnRlbnRdKTsgLy8g5L6d6LWWIHByb2Nlc3NDb250ZW50IOWSjOa2iOaBr+WGheWuuVxyXG5cclxuICAvLyDlpoLmnpzmmK/liqnmiYvmtojmga/kuJTljIXlkKvlvJXnlKjvvIzkvb/nlKjoh6rlrprkuYnmuLLmn5NcclxuICBpZiAobWVzc2FnZS5yb2xlID09PSBcImFzc2lzdGFudFwiICYmIG1lc3NhZ2UuY29udGVudC5pbmNsdWRlcyhcIltjaXRhdGlvbjpcIikpIHtcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxkaXZcclxuICAgICAgICBjbGFzc05hbWU9e2BmbGV4ICR7XHJcbiAgICAgICAgICBtZXNzYWdlLnJvbGUgPT09IFwidXNlclwiID8gXCJqdXN0aWZ5LWVuZFwiIDogXCJqdXN0aWZ5LXN0YXJ0XCJcclxuICAgICAgICB9IG1iLTQgY2hhdC1tZXNzYWdlYH1cclxuICAgICAgPlxyXG4gICAgICAgIDxkaXZcclxuICAgICAgICAgIGNsYXNzTmFtZT17YG1heC13LVs4MCVdIHJvdW5kZWQtbGcgcHgtNCBweS0zICR7XHJcbiAgICAgICAgICAgIG1lc3NhZ2Uucm9sZSA9PT0gXCJ1c2VyXCJcclxuICAgICAgICAgICAgICA/IFwiYmctcHJpbWFyeSB0ZXh0LXByaW1hcnktZm9yZWdyb3VuZFwiXHJcbiAgICAgICAgICAgICAgOiBcImJnLW11dGVkIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCBib3JkZXIgYm9yZGVyLWJvcmRlclwiXHJcbiAgICAgICAgICB9IHNoYWRvdy1zbWB9XHJcbiAgICAgICAgPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwcm9zZSBwcm9zZS1zbSBtYXgtdy1ub25lIGRhcms6cHJvc2UtaW52ZXJ0XCI+XHJcbiAgICAgICAgICAgIHtyZW5kZXJDb250ZW50KG1lc3NhZ2UuY29udGVudCl9XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIHtpc0xvYWRpbmcgJiYgKFxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIG10LTMgc3BhY2UteC0yIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0zIGgtMyBib3JkZXItMiBib3JkZXItY3VycmVudCBib3JkZXItdC10cmFuc3BhcmVudCByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1zcGluXCI+PC9kaXY+XHJcbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14c1wiPuato+WcqOaAneiAgy4uLjwvc3Bhbj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICApfVxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgICk7XHJcbiAgfVxyXG5cclxuICAvLyDlr7nkuo7lhbbku5bmtojmga/vvIzkvb/nlKjpu5jorqTnmoRDaGF0TWVzc2FnZee7hOS7tlxyXG4gIHJldHVybiA8Q2hhdE1lc3NhZ2UgbWVzc2FnZT17bWVzc2FnZX0gaXNMb2FkaW5nPXtpc0xvYWRpbmd9IC8+O1xyXG59XHJcbiJdLCJuYW1lcyI6WyJDaXRhdGlvblRvb2x0aXAiLCJ1c2VNZW1vIiwiQ3VzdG9tQ2hhdE1lc3NhZ2UiLCJtZXNzYWdlIiwiaXNMb2FkaW5nIiwicHJvY2Vzc0NvbnRlbnQiLCJjb250ZW50IiwiY2l0YXRpb25SZWdleCIsImNpdGF0aW9ucyIsIm1hdGNoIiwiZXhlYyIsInB1c2giLCJwcm9jZXNzZWRDb250ZW50IiwiZm9yRWFjaCIsImNpdGF0aW9uSWQiLCJpbmRleCIsImNpdGF0aW9uTnVtYmVyIiwiY2l0YXRpb25QYXR0ZXJuIiwiUmVnRXhwIiwicmVwbGFjZSIsInJlbmRlckNvbnRlbnQiLCJwYXJ0cyIsInNwbGl0IiwibWFwIiwicGFydCIsImNpdGF0aW9uTWF0Y2giLCJyb2xlIiwiaW5jbHVkZXMiLCJkaXYiLCJjbGFzc05hbWUiLCJzcGFuIiwiQ2hhdE1lc3NhZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/CustomChatMessage.tsx\n"));

/***/ })

});